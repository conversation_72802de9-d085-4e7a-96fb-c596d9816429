import WebSocket from 'ws';
import { AudioProcessor } from '../audio/audio-processor.js';
import { TranscriptionManager } from '../audio/transcription-manager.js';
import { scriptManager } from '../scripts/script-manager.js';
import { Modality } from '../gemini/client.js';

// WebSocket handlers for different connection types
export function registerWebSocketHandlers(fastify, dependencies) {
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        recoveryManager,
        voiceManager,
        modelManager,
        GEMINI_DEFAULT_VOICE,
        GEMINI_DEFAULT_MODEL,
        SUMMARY_GENERATION_PROMPT
    } = dependencies;

    // Initialize global transcription manager
    const transcriptionManager = new TranscriptionManager();

    // Add transcription manager to dependencies for consistency
    const enhancedDependencies = {
        ...dependencies,
        transcriptionManager
    };

    // 1. OUTBOUND CALLS - Twilio media stream for outbound calls
    fastify.register(async (fastify) => {
        fastify.get('/media-stream', { websocket: true }, (connection, req) => {
            handleOutboundCall(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: false
            });
        });
    });

    // 2. INBOUND CALLS - Twilio media stream for inbound calls
    fastify.register(async (fastify) => {
        fastify.get('/media-stream-inbound', { websocket: true }, (connection, req) => {
            handleInboundCall(connection, {
                ...enhancedDependencies,
                callType: 'inbound',
                isTestMode: false
            });
        });
    });

    // 3. OUTBOUND TESTING MODE - Local audio testing for outbound scripts
    fastify.register(async (fastify) => {
        fastify.get('/test-outbound', { websocket: true }, (connection, req) => {
            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: true
            });
        });
    });

    // 4. INBOUND TESTING MODE - Local audio testing for inbound scripts
    fastify.register(async (fastify) => {
        fastify.get('/test-inbound', { websocket: true }, (connection, req) => {
            handleInboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'inbound',
                isTestMode: true
            });
        });
    });

    // Legacy endpoint for backward compatibility
    fastify.register(async (fastify) => {
        fastify.get('/local-audio-session', { websocket: true }, (connection, req) => {
            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: true
            });
        });
    });
}

// 1. OUTBOUND CALLS - Handle real outbound calls via Twilio
function handleOutboundCall(connection, deps) {
    const { callType, isTestMode } = deps;
    console.log(`📞 [OUTBOUND] Client connected for outbound call (test: ${isTestMode})`);

    return handleTwilioFlow(connection, {
        ...deps,
        flowType: 'outbound_call',
        getSessionConfig: () => getOutboundCallConfig(deps),
        isIncomingCall: false
    });
}

// 2. INBOUND CALLS - Handle real inbound calls via Twilio
function handleInboundCall(connection, deps) {
    const { callType, isTestMode } = deps;
    console.log(`📞 [INBOUND] Client connected for inbound call (test: ${isTestMode})`);

    return handleTwilioFlow(connection, {
        ...deps,
        flowType: 'inbound_call',
        getSessionConfig: () => getInboundCallConfig(deps),
        isIncomingCall: true
    });
}

// 3. OUTBOUND TESTING MODE - Test outbound scripts locally
function handleOutboundTesting(connection, deps) {
    const { callType, isTestMode } = deps;
    console.log(`🧪 [OUTBOUND TEST] Client connected for outbound testing`);

    return handleLocalTestingFlow(connection, {
        ...deps,
        flowType: 'outbound_test',
        getSessionConfig: () => getOutboundTestConfig(deps),
        isIncomingCall: false
    });
}

// 4. INBOUND TESTING MODE - Test inbound scripts locally
function handleInboundTesting(connection, deps) {
    const { callType, isTestMode } = deps;
    console.log(`🧪 [INBOUND TEST] Client connected for inbound testing`);

    return handleLocalTestingFlow(connection, {
        ...deps,
        flowType: 'inbound_test',
        getSessionConfig: () => getInboundTestConfig(deps),
        isIncomingCall: true
    });
}

// Common Twilio flow handler (for both inbound and outbound calls)
function handleTwilioFlow(connection, deps) {
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        recoveryManager,
        flowType,
        getSessionConfig,
        isIncomingCall,
        SUMMARY_GENERATION_PROMPT
    } = deps;

    let callSid = 'pending';
    console.log(`🔌 [${flowType.toUpperCase()}] Client connected`);

    let streamSid = null;
    let geminiSession = null;
    let isSessionActive = false;

    // Get flow-specific configuration
    const sessionConfig = getSessionConfig();

    console.log(`📞 [${callSid}] Flow: ${flowType}, Incoming: ${isIncomingCall}`);
    console.log(`🤖 [${callSid}] Using model: ${sessionConfig.model}, voice: ${sessionConfig.voice}`);

    connection.socket.on('message', async (message) => {
        try {
            const data = JSON.parse(message);

            switch (data.event) {
                case 'connected':
                    console.log(`📞 [${callSid}] Connected event received`);
                    break;

                case 'start':
                    streamSid = data.start.streamSid;
                    callSid = data.start.callSid;
                    console.log(`🚀 [${callSid}] Stream started: ${streamSid}`);

                    // Store enhanced connection data
                    const connectionData = {
                        twilioWs: connection.socket,
                        streamSid,
                        callSid,
                        isSessionActive: false,
                        summaryRequested: false,
                        summaryReceived: false,
                        summaryText: '',
                        conversationLog: [],
                        fullTranscript: [],
                        speechTranscript: [], // For Deepgram transcriptions
                        isIncomingCall,
                        sessionType: 'twilio',
                        sessionStartTime: Date.now(),
                        lastActivity: Date.now(),
                        targetName: sessionConfig.targetName,
                        targetPhoneNumber: sessionConfig.targetPhoneNumber,
                        originalAIInstructions: sessionConfig.aiInstructions,
                        deepgramConnection: null
                    };
                    activeConnections.set(callSid, connectionData);

                    // Start session lifecycle management
                    lifecycleManager.startSession(callSid, connectionData, sessionConfig);

                    // Track connection health
                    healthMonitor.trackConnection(callSid, 'connected', {
                        streamSid,
                        isIncomingCall,
                        sessionType: 'twilio'
                    });

                    // Create Gemini session
                    geminiSession = await sessionManager.createGeminiSession(callSid, sessionConfig, connectionData);
                    if (geminiSession) {
                        connectionData.geminiSession = geminiSession;
                        isSessionActive = true;

                        // Initialize Deepgram transcription for conversation logging
                        try {
                            const dgConnection = await transcriptionManager.createTranscription(callSid, connectionData);
                            if (dgConnection) {
                                connectionData.deepgramConnection = dgConnection;
                                console.log(`✅ [${callSid}] Deepgram transcription initialized`);
                            }
                        } catch (error) {
                            console.warn(`⚠️ [${callSid}] Failed to initialize Deepgram transcription:`, error);
                        }
                    }
                    break;

                case 'media':
                    if (geminiSession && isSessionActive && data.media.payload) {
                        try {
                            const audioBuffer = Buffer.from(data.media.payload, 'base64');
                            await sessionManager.sendAudioToGemini(callSid, geminiSession, audioBuffer);

                            // Send audio to Deepgram for transcription
                            const connectionData = activeConnections.get(callSid);
                            if (connectionData && connectionData.deepgramConnection) {
                                try {
                                    transcriptionManager.sendAudioToTranscription(callSid, audioBuffer);
                                } catch (dgError) {
                                    console.warn(`⚠️ [${callSid}] Deepgram send error:`, dgError);
                                }
                            }

                            // Update activity for session persistence
                            lifecycleManager.updateActivity(callSid);
                        } catch (error) {
                            console.error(`❌ [${callSid}] Error processing audio:`, error);

                            // Check if we need to recover the session
                            const connectionData = activeConnections.get(callSid);
                            if (connectionData && recoveryManager.needsRecovery(callSid, activeConnections)) {
                                console.log(`🔄 [${callSid}] Audio processing failed, attempting session recovery`);
                                await recoveryManager.recoverSession(callSid, 'audio_processing_error', activeConnections);
                            }
                        }
                    }
                    break;

                case 'stop':
                    console.log(`🛑 [${callSid}] Stream stopped - USER INITIATED SESSION END`);

                    // This is a user-initiated session end - proceed with proper cleanup
                    const stopConnectionData = activeConnections.get(callSid);
                    if (stopConnectionData) {
                        // Clean up Deepgram transcription
                        if (stopConnectionData.deepgramConnection) {
                            transcriptionManager.closeTranscription(callSid);
                        }

                        // Use lifecycle manager for proper session ending with summary
                        await lifecycleManager.requestSessionEnd(callSid, stopConnectionData, 'user_stop_stream');
                    } else {
                        console.warn(`⚠️ [${callSid}] No connection data found for user-initiated stop`);
                        endSession(callSid, { sessionManager, contextManager, activeConnections, lifecycleManager, transcriptionManager });
                    }
                    break;

                default:
                    console.log(`❓ [${callSid}] Unknown event: ${data.event}`);
            }

        } catch (error) {
            console.error(`❌ [${callSid}] Error processing message:`, error);
        }
    });

    connection.socket.on('close', () => {
        console.log(`🔌 [${callSid}] WebSocket connection closed - USER INITIATED SESSION END`);
        // This is a user-initiated session end via connection close
        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // Clean up Deepgram transcription
            if (connectionData.deepgramConnection) {
                transcriptionManager.closeTranscription(callSid);
            }

            if (lifecycleManager) {
                lifecycleManager.requestSessionEnd(callSid, connectionData, 'user_close_connection');
            } else {
                endSession(callSid, { sessionManager, contextManager, activeConnections, lifecycleManager, transcriptionManager });
            }
        } else {
            endSession(callSid, { sessionManager, contextManager, activeConnections, lifecycleManager, transcriptionManager });
        }
    });

    connection.socket.on('error', (error) => {
        console.error(`❌ [${callSid}] WebSocket error:`, error);
        // WebSocket error - try to recover session instead of ending it
        const connectionData = activeConnections.get(callSid);
        if (connectionData && recoveryManager && contextManager.canRecover(callSid)) {
            console.log(`🔄 [${callSid}] WebSocket error detected, attempting session recovery`);
            contextManager.markSessionInterrupted(callSid, 'websocket_error');
            // Don't end session immediately - let recovery manager handle it
            setTimeout(() => {
                recoveryManager.recoverSession(callSid, 'websocket_error', activeConnections);
            }, 1000);
        } else {
            // Only end session if recovery is not possible
            endSession(callSid, { sessionManager, contextManager, activeConnections, lifecycleManager });
        }
    });
}

// Common local testing flow handler (for both inbound and outbound testing)
function handleLocalTestingFlow(connection, deps) {
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        flowType,
        getSessionConfig,
        isIncomingCall,
        SUMMARY_GENERATION_PROMPT
    } = deps;

    const sessionId = `${flowType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    console.log(`🧪 [${sessionId}] ${flowType.toUpperCase()} testing session started`);
    console.log(`🔍 DEBUG: [${sessionId}] Setting up message handler for ${flowType} testing`);
    console.log(`🔍 DEBUG: [${sessionId}] Step 1: About to check WebSocket state`);

    try {
        console.log(`🔍 DEBUG: [${sessionId}] Connection object:`, !!connection);
        console.log(`🔍 DEBUG: [${sessionId}] Connection keys:`, Object.keys(connection));
        console.log(`🔍 DEBUG: [${sessionId}] Socket object:`, !!connection.socket);

        // Try different possible WebSocket references
        const ws = connection.socket || connection;
        console.log(`🔍 DEBUG: [${sessionId}] Using WebSocket:`, !!ws);
        console.log(`🔍 DEBUG: [${sessionId}] WebSocket readyState:`, ws.readyState);
        console.log(`🔍 DEBUG: [${sessionId}] WebSocket protocol:`, ws.protocol);
        console.log(`🔍 DEBUG: [${sessionId}] Step 3: About to declare variables`);
    } catch (wsError) {
        console.error(`❌ [${sessionId}] Error checking WebSocket state:`, wsError);
        console.error(`❌ [${sessionId}] Error stack:`, wsError.stack);
        // Don't return early - continue with handler setup
        console.log(`🔍 DEBUG: [${sessionId}] Continuing with handler setup despite WebSocket error`);
    }

    let geminiSession = null;
    let isSessionActive = false;
    console.log(`🔍 DEBUG: [${sessionId}] Step 4: Variables declared, about to attach message handler`);

    // Use the correct WebSocket reference
    const ws = connection.socket || connection;
    console.log(`🔍 DEBUG: [${sessionId}] Attaching message handler to:`, !!ws);

    ws.on('message', async (message) => {
        console.log(`🔍 DEBUG: [${sessionId}] MESSAGE HANDLER CALLED!`);
        console.log(`🔍 DEBUG: [${sessionId}] Raw message received:`, message.toString().substring(0, 200));
        try {
            const data = JSON.parse(message);
            console.log(`🔍 DEBUG: [${sessionId}] Parsed message type: ${data.type}`);
            console.log(`🔍 DEBUG: [${sessionId}] Message data keys:`, Object.keys(data));

            console.log(`🔍 [${sessionId}] ===== MESSAGE SWITCH DEBUG =====`);
            console.log(`🔍 [${sessionId}] data.type = "${data.type}"`);
            console.log(`🔍 [${sessionId}] About to enter switch statement`);
            console.log(`🔍 [${sessionId}] =====================================`);

            switch (data.type) {
                case 'start-session':
                    console.log(`🔍 [${sessionId}] ===== ENTERED START-SESSION CASE =====`);
                    console.log(`🚀 [${sessionId}] Starting ${flowType} testing session`);
                    console.log(`🔍 [${sessionId}] ===== START SESSION DEBUG =====`);
                    console.log(`🔍 [${sessionId}] data.model = "${data.model}"`);
                    console.log(`🔍 [${sessionId}] data.voice = "${data.voice}"`);
                    console.log(`🔍 [${sessionId}] deps.GEMINI_DEFAULT_MODEL = "${deps.GEMINI_DEFAULT_MODEL}"`);
                    console.log(`🔍 [${sessionId}] deps.GEMINI_DEFAULT_VOICE = "${deps.GEMINI_DEFAULT_VOICE}"`);
                    console.log(`🔍 [${sessionId}] ================================`);

                    try {
                        // Get flow-specific configuration
                        console.log(`🔍 DEBUG: [${sessionId}] About to call getSessionConfig for ${flowType}`);
                        let sessionConfig = getSessionConfig();
                        console.log(`🔍 DEBUG: [${sessionId}] getSessionConfig returned:`, !!sessionConfig);
                        if (sessionConfig) {
                            console.log(`🔍 DEBUG: [${sessionId}] Initial session config:`, {
                                hasAiInstructions: !!sessionConfig.aiInstructions,
                                aiInstructionsLength: sessionConfig.aiInstructions?.length || 0,
                                voice: sessionConfig.voice,
                                model: sessionConfig.model,
                                scriptType: sessionConfig.scriptType,
                                scriptId: sessionConfig.scriptId
                            });
                        } else {
                            console.error(`❌ [${sessionId}] getSessionConfig returned null/undefined for ${flowType}`);
                        }

                        // Allow override from client for testing - use campaign script as-is
                        if (data.aiInstructions) {
                            sessionConfig.aiInstructions = data.aiInstructions;
                        }
                        if (data.voice) {
                            sessionConfig.voice = deps.voiceManager.getValidGeminiVoice(data.voice);
                        }
                        if (data.model) {
                            sessionConfig.model = deps.modelManager.getValidGeminiModel(data.model);
                        }
                        if (data.scriptId) {
                            // Load specific script for testing
                            try {
                                const testConfig = deps.scriptManager.getScriptConfig(data.scriptId, isIncomingCall);
                                if (testConfig) {
                                    sessionConfig = {
                                        ...testConfig,
                                        aiInstructions: `[TESTING MODE] ${testConfig.aiInstructions}`,
                                        isTestMode: true
                                    };
                                }
                            } catch (error) {
                                console.warn(`⚠️ [${sessionId}] Error loading test script ${data.scriptId}:`, error);
                            }
                        }

                        // Store enhanced connection data
                    const connectionData = {
                        localWs: connection.socket || connection,
                        sessionId,
                        isSessionActive: false,
                        summaryRequested: false,
                        summaryReceived: false,
                        summaryText: '',
                        conversationLog: [],
                        fullTranscript: [],
                        speechTranscript: [],
                        isIncomingCall,
                        sessionType: 'local_test',
                        flowType,
                        sessionStartTime: Date.now(),
                        lastActivity: Date.now(),
                        targetName: sessionConfig.targetName || 'Test Contact',
                        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
                        originalAIInstructions: sessionConfig.aiInstructions,
                        scriptId: sessionConfig.scriptId,
                        isTestMode: true
                    };
                    activeConnections.set(sessionId, connectionData);

                    // Start session lifecycle management
                    // TEMPORARILY DISABLED FOR DEBUGGING: lifecycleManager.startSession(sessionId, connectionData, sessionConfig);
                    console.log(`🔍 [${sessionId}] SKIPPING lifecycle manager startSession for debugging`);

                    // Track connection health
                    healthMonitor.trackConnection(sessionId, 'connected', {
                        flowType,
                        isTestMode: true,
                        scriptId: sessionConfig.scriptId
                    });

                    // Create Gemini session
                    console.log(`🔍 DEBUG: [${sessionId}] About to create Gemini session...`);
                    console.log(`🔍 DEBUG: [${sessionId}] Session config:`, {
                        hasAiInstructions: !!sessionConfig.aiInstructions,
                        aiInstructionsLength: sessionConfig.aiInstructions?.length || 0,
                        voice: sessionConfig.voice,
                        model: sessionConfig.model,
                        scriptId: sessionConfig.scriptId
                    });

                        // Create Gemini session DIRECTLY (using working pattern)
                        console.log(`🤖 [${sessionId}] Creating DIRECT Gemini session (bypassing session manager)...`);
                        console.log(`🔍 [${sessionId}] DEBUG: About to create session with model: "${sessionConfig.model}"`);

                        const sessionStartTime = Date.now();

                        try {
                            console.log(`🔍 [${sessionId}] DEBUG: sessionConfig.model = "${sessionConfig.model}"`);
                            console.log(`🔍 [${sessionId}] DEBUG: typeof sessionConfig.model = ${typeof sessionConfig.model}`);
                            console.log(`🔍 [${sessionId}] DEBUG: sessionConfig keys:`, Object.keys(sessionConfig));
                            console.log(`🔍 [${sessionId}] DEBUG: Full sessionConfig:`, JSON.stringify(sessionConfig, null, 2));

                            // DEBUG: Check what model and voice we're getting from sessionConfig
                            console.log(`🔍 [${sessionId}] ===== MODEL DEBUG =====`);
                            console.log(`🔍 [${sessionId}] sessionConfig.model = "${sessionConfig.model}"`);
                            console.log(`🔍 [${sessionId}] sessionConfig.voice = "${sessionConfig.voice}"`);
                            console.log(`🔍 [${sessionId}] deps.GEMINI_DEFAULT_MODEL = "${deps.GEMINI_DEFAULT_MODEL}"`);
                            console.log(`🔍 [${sessionId}] deps.GEMINI_DEFAULT_VOICE = "${deps.GEMINI_DEFAULT_VOICE}"`);
                            console.log(`🔍 [${sessionId}] process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);
                            console.log(`🔍 [${sessionId}] process.env.MODEL = "${process.env.MODEL}"`);
                            console.log(`🔍 [${sessionId}] ========================`);

                            // Use the correct model and voice from environment/managers
                            const correctModel = sessionConfig.model || deps.GEMINI_DEFAULT_MODEL;
                            const correctVoice = sessionConfig.voice || deps.GEMINI_DEFAULT_VOICE;
                            console.log(`🔍 [${sessionId}] FINAL: Using model: "${correctModel}", voice: "${correctVoice}"`);

                            geminiSession = await deps.sessionManager.geminiClient.live.connect({
                                model: correctModel,
                                callbacks: {
                                    onopen: () => {
                                        console.log(`✅ [${sessionId}] DIRECT Gemini session opened successfully`);
                                        isSessionActive = true;
                                    },

                                    onmessage: async (message) => {
                                        try {
                                            // Handle setupComplete message - CRITICAL FROM OLD IMPLEMENTATION
                                            if (message.setupComplete) {
                                                console.log(`✅ [${sessionId}] DIRECT Gemini setup complete - session ready for conversation`);
                                                return;
                                            }

                                            // Handle goAway message - CRITICAL FROM OLD IMPLEMENTATION
                                            if (message.goAway) {
                                                console.log(`⚠️ [${sessionId}] Gemini session ending - goAway received:`, message.goAway);
                                                console.log(`🔄 [${sessionId}] Session will close in:`, message.goAway.timeLeft);
                                                console.log(`🔄 [${sessionId}] IGNORING goAway - keeping session alive until user manually stops`);
                                                // REQUIREMENT: Keep session alive as long as call/test is active
                                                // Don't mark session as inactive - ignore Gemini's termination request
                                                console.log(`✅ [${sessionId}] Session kept alive - user controls session lifecycle, not Gemini`);
                                                return;
                                            }

                                            // Log Gemini API messages for debugging (excluding audio packets)
                                            const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                                            if (!hasAudio) {
                                                console.log(`📨 [${sessionId}] DIRECT Gemini API message (non-audio):`, JSON.stringify(message, null, 2));
                                            } else {
                                                console.log(`🎵 [${sessionId}] DIRECT Gemini audio packet received (${message.serverContent?.modelTurn?.parts?.[0]?.inlineData?.data?.length || 0} bytes)`);

                                                try {
                                                    console.log(`🔍 [${sessionId}] AUDIO PACKET PROCESSING STARTED - about to process audio forwarding`);

                                                    // Handle audio response from Gemini - SIMPLIFIED LIKE OLD IMPLEMENTATION
                                                    const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;

                                                console.log(`🔍 [${sessionId}] Audio forwarding debug: audio exists: ${!!audio}, data length: ${audio?.data?.length || 0}`);

                                                // Get connectionData from activeConnections since it's not in scope here
                                                const currentConnectionData = deps.activeConnections.get(sessionId);
                                                console.log(`🔍 [${sessionId}] ConnectionData debug: currentConnectionData exists: ${!!currentConnectionData}, localWs readyState: ${currentConnectionData?.localWs?.readyState}`);

                                                // SIMPLIFIED CHECK - just like old implementation for local WebSocket
                                                if (audio && currentConnectionData?.localWs && currentConnectionData.localWs.readyState === 1) {
                                                    console.log(`🔊 [${sessionId}] Sending audio response to client, size: ${audio.data?.length || 0}`);

                                                    try {
                                                        // Send audio back to client - EXACTLY like old implementation
                                                        currentConnectionData.localWs.send(JSON.stringify({
                                                            type: 'audio',
                                                            audio: audio.data
                                                        }));
                                                        console.log(`✅ [${sessionId}] Audio sent successfully, connection still open: ${currentConnectionData.localWs.readyState === 1}`);
                                                    } catch (error) {
                                                        console.error(`❌ [${sessionId}] Error forwarding audio to local WebSocket:`, error);
                                                    }
                                                } else {
                                                    console.log(`📝 [${sessionId}] Received non-audio message from Gemini or connection not ready`);
                                                }
                                                } catch (error) {
                                                    console.error(`❌ [${sessionId}] Error in audio packet processing:`, error);
                                                }
                                            }

                                            // Handle turnComplete message - FROM OLD IMPLEMENTATION
                                            console.log(`🔍 [${sessionId}] Checking turnComplete: ${!!message.serverContent?.turnComplete}`);
                                            if (message.serverContent?.turnComplete) {
                                                console.log(`🎯 [${sessionId}] Turn complete - waiting for next user input, keeping session alive`);
                                                return;
                                            }
                                            console.log(`🔍 [${sessionId}] Passed turnComplete check, continuing to audio processing`);

                                            // Enhanced message validation and handling
                                            if (!message || !message.serverContent) {
                                                console.warn(`⚠️ [${sessionId}] Received invalid message structure`);
                                                return;
                                            }

                                            // Handle audio response from Gemini with validation
                                            const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;

                                            console.log(`🔍 [${sessionId}] Audio detection debug: audio exists: ${!!audio}, mimeType: ${audio?.mimeType}, includes audio: ${audio?.mimeType?.includes('audio')}`);

                                            if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                                                // Validate audio data before processing
                                                if (!audio.data || audio.data.length === 0) {
                                                    console.warn(`⚠️ [${sessionId}] Received empty audio data from Gemini`);
                                                    return;
                                                }

                                                // Debug WebSocket state
                                                console.log(`🔍 [${sessionId}] Audio forwarding debug: localWs exists: ${!!connectionData.localWs}, readyState: ${connectionData.localWs?.readyState}, expected: 1`);

                                                // Send audio to local WebSocket if connection is active
                                                if (connectionData.localWs && connectionData.localWs.readyState === 1) { // WebSocket.OPEN
                                                    try {
                                                        connectionData.localWs.send(JSON.stringify({
                                                            type: 'audio-response',
                                                            audio: {
                                                                mimeType: audio.mimeType,
                                                                data: audio.data
                                                            }
                                                        }));
                                                        console.log(`🔊 [${sessionId}] Sent audio to local WebSocket (${audio.data.length} chars)`);
                                                    } catch (audioError) {
                                                        console.error(`❌ [${sessionId}] Error sending audio to local WebSocket:`, audioError);
                                                    }
                                                }
                                            }

                                            // Handle text response for summary collection and conversation logging
                                            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                                            if (text) {
                                                console.log(`💬 [${sessionId}] DIRECT Gemini response: ${text.substring(0, 100)}...`);

                                                // Update AI responsiveness tracking
                                                connectionData.lastAIResponse = Date.now();
                                                connectionData.responseTimeouts = 0; // Reset timeout counter
                                                connectionData.connectionQuality = 'good';
                                            }

                                        } catch (error) {
                                            console.error(`❌ [${sessionId}] Error processing DIRECT Gemini message:`, error);
                                        }
                                    },

                                    onerror: (error) => {
                                        console.error(`❌ [${sessionId}] DIRECT Gemini session error:`, error);
                                        isSessionActive = false;
                                    },

                                    onclose: (event) => {
                                        console.log(`🔌 [${sessionId}] DIRECT Gemini session closed`);
                                        console.log(`🔍 [${sessionId}] Close event details:`, event);
                                        console.log(`🔍 [${sessionId}] Session was active for: ${Date.now() - sessionStartTime}ms`);
                                        console.log(`🔍 [${sessionId}] Stack trace:`, new Error().stack);
                                        isSessionActive = false;
                                    }
                                },
                                config: {
                                    responseModalities: [Modality.AUDIO],
                                    speechConfig: {
                                        voiceConfig: {
                                            prebuiltVoiceConfig: {
                                                voiceName: correctVoice
                                            }
                                        }
                                    }
                                },
                                temperature: 1.1,
                                topP: 0.95,
                                topK: 40,
                                maxOutputTokens: 8192,
                                responseMimeType: 'text/plain'
                            });

                            connectionData.geminiSession = geminiSession;
                            console.log(`✅ [${sessionId}] DIRECT Gemini session created successfully for ${flowType} testing`);

                        } catch (error) {
                            console.error(`❌ [${sessionId}] Error creating DIRECT Gemini session:`, error);
                            geminiSession = null;
                        }

                        if (geminiSession) {

                            // Send initial AI prepare message with delay (like old implementation)
                            setTimeout(async () => {
                                if (geminiSession && activeConnections.has(sessionId)) {
                                    try {
                                        console.log(`🎯 [${sessionId}] Now sending ${flowType} prepare message after 1-second delay`);
                                        await deps.sessionManager.sendInitialMessage(geminiSession, sessionConfig.aiInstructions);
                                        console.log(`✅ [${sessionId}] Initial AI message sent successfully`);
                                    } catch (initError) {
                                        console.warn(`⚠️ [${sessionId}] Failed to send initial AI message:`, initError);
                                    }
                                } else {
                                    console.log(`⚠️ [${sessionId}] Session no longer active, skipping prepare message`);
                                }
                            }, 1000); // 1-second delay to ensure session is ready

                            // Initialize Deepgram transcription for local testing
                            try {
                                const dgConnection = await deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData);
                                if (dgConnection) {
                                    connectionData.deepgramConnection = dgConnection;
                                    console.log(`✅ [${sessionId}] Local Deepgram transcription initialized for ${flowType} testing`);
                                }
                            } catch (error) {
                                console.warn(`⚠️ [${sessionId}] Failed to initialize local Deepgram transcription:`, error);
                            }

                            ws.send(JSON.stringify({
                                type: 'session-started',
                                sessionId: sessionId,
                                flowType: flowType,
                                scriptId: sessionConfig.scriptId,
                                config: {
                                    voice: sessionConfig.voice,
                                    model: sessionConfig.model,
                                    isIncomingCall: isIncomingCall,
                                    transcriptionEnabled: !!connectionData.deepgramConnection
                                }
                            }));
                            console.log(`✅ [${sessionId}] ${flowType.toUpperCase()} testing session started successfully`);
                        } else {
                            console.error(`❌ [${sessionId}] Gemini session creation returned null/undefined`);
                            ws.send(JSON.stringify({
                                type: 'session-error',
                                error: 'Failed to create Gemini session'
                            }));
                        }

                    } catch (startSessionError) {
                        console.error(`❌ [${sessionId}] Critical error in start-session for ${flowType}:`, startSessionError);
                        console.error(`❌ [${sessionId}] Error stack:`, startSessionError.stack);
                        try {
                            ws.send(JSON.stringify({
                                type: 'session-error',
                                error: `Session start failed: ${startSessionError.message}`
                            }));
                        } catch (sendError) {
                            console.error(`❌ [${sessionId}] Failed to send error message:`, sendError);
                        }
                    }
                    break;

                case 'audio-data':
                    if (geminiSession && isSessionActive && (data.audioData || data.audio)) {
                        try {
                            // Update activity for session persistence
                            lifecycleManager.updateActivity(sessionId);

                            // Handle browser audio data (support both audioData and audio fields)
                            const audioBuffer = Buffer.from(data.audioData || data.audio, 'base64');
                            console.log(`🔍 [${sessionId}] About to call sendAudioToGemini - audioBuffer size: ${audioBuffer.length}, geminiSession exists: ${!!geminiSession}`);
                            await deps.sessionManager.sendAudioToGemini(sessionId, geminiSession, audioBuffer);

                            // Send audio to Deepgram for transcription (local testing)
                            const connectionData = activeConnections.get(sessionId);
                            if (connectionData && connectionData.deepgramConnection) {
                                try {
                                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                                } catch (dgError) {
                                    console.warn(`⚠️ [${sessionId}] Local Deepgram send error:`, dgError);
                                }
                            }
                        } catch (error) {
                            console.error(`❌ [${sessionId}] Error processing ${flowType} testing audio:`, error);

                            // Check if we need to recover the session
                            const connectionData = activeConnections.get(sessionId);
                            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                                console.log(`🔄 [${sessionId}] Audio processing failed in ${flowType} testing, attempting session recovery`);
                                await recoveryManager.recoverSession(sessionId, 'audio_processing_error', activeConnections);
                            }
                        }
                    }
                    break;

                case 'text-message':
                    if (geminiSession && isSessionActive && data.text) {
                        try {
                            console.log(`💬 [${sessionId}] Received text message: "${data.text}"`);
                            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
                        } catch (error) {
                            console.error(`❌ [${sessionId}] Error sending text to Gemini:`, error);
                        }
                    }
                    break;

                case 'turn-complete':
                    if (geminiSession && isSessionActive) {
                        try {
                            console.log(`🔚 [${sessionId}] Received turn complete signal - telling AI to respond`);
                            await deps.sessionManager.sendTurnComplete(sessionId, geminiSession);
                        } catch (error) {
                            console.error(`❌ [${sessionId}] Error sending turn complete:`, error);
                        }
                    }
                    break;

                case 'end-session':
                    console.log(`🔚 [${sessionId}] Ending ${flowType} testing session - USER INITIATED`);
                    // This is a user-initiated session end for testing
                    const endConnectionData = activeConnections.get(sessionId);
                    if (endConnectionData) {
                        // Clean up Deepgram transcription
                        if (endConnectionData.deepgramConnection) {
                            transcriptionManager.closeTranscription(sessionId);
                        }

                        if (lifecycleManager) {
                            await lifecycleManager.requestSessionEnd(sessionId, endConnectionData, 'user_end_testing');
                        } else {
                            await endSession(sessionId, { ...deps, transcriptionManager }, 'user_requested');
                        }
                    } else {
                        await endSession(sessionId, { ...deps, transcriptionManager }, 'user_requested');
                    }
                    break;

                case 'request-summary':
                    console.log(`📝 [${sessionId}] Manual summary requested for ${flowType} testing`);
                    const summaryConnectionData = activeConnections.get(sessionId);
                    if (summaryConnectionData && summaryManager) {
                        await summaryManager.requestSummary(sessionId, summaryConnectionData, contextManager);
                    }
                    break;

                default:
                    console.log(`❓ [${sessionId}] Unknown ${flowType} testing event: ${data.type}`);
            }

        } catch (error) {
            console.error(`❌ [${sessionId}] Error processing ${flowType} testing message:`, error);
        }
    });

    ws.on('close', (code, reason) => {
        console.log(`🔌 [${sessionId}] ${flowType.toUpperCase()} testing connection closed - Code: ${code}, Reason: ${reason || 'No reason'}`);
        // This is a user-initiated session end via connection close
        const connectionData = activeConnections.get(sessionId);
        if (connectionData) {
            // Clean up Deepgram transcription
            if (connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(sessionId);
            }

            if (deps.lifecycleManager) {
                deps.lifecycleManager.requestSessionEnd(sessionId, connectionData, 'user_close_testing');
            } else {
                endSession(sessionId, deps, 'connection_closed');
            }
        } else {
            endSession(sessionId, deps, 'connection_closed');
        }
    });

    ws.on('error', (error) => {
        console.error(`❌ [${sessionId}] ${flowType.toUpperCase()} testing error:`, error);
        console.error(`❌ [${sessionId}] Error stack:`, error.stack);
        // WebSocket error in testing - try to recover session instead of ending it
        const connectionData = activeConnections.get(sessionId);
        if (connectionData && recoveryManager && contextManager.canRecover(sessionId)) {
            console.log(`🔄 [${sessionId}] Testing WebSocket error detected, attempting session recovery`);
            contextManager.markSessionInterrupted(sessionId, 'testing_websocket_error');
            // Don't end session immediately - let recovery manager handle it
            setTimeout(() => {
                recoveryManager.recoverSession(sessionId, 'testing_websocket_error', activeConnections);
            }, 1000);
        } else {
            // Clean up Deepgram transcription before ending session
            if (connectionData && connectionData.deepgramConnection) {
                transcriptionManager.closeTranscription(sessionId);
            }
            // Only end session if recovery is not possible
            endSession(sessionId, { ...deps, transcriptionManager }, 'connection_error');
        }
    });
}

// 1. OUTBOUND CALL Configuration
function getOutboundCallConfig(deps) {
    try {
        const currentScript = deps.scriptManager.getCurrentOutboundScript();
        if (currentScript) {
            return deps.scriptManager.getScriptConfig(currentScript.id, false);
        }
    } catch (error) {
        console.warn('⚠️ Error getting outbound script config:', error);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(deps.GEMINI_DEFAULT_VOICE);
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // Fallback to default outbound config - no hardcoded instructions
    return {
        aiInstructions: '', // Campaign script should provide all instructions
        voice: validVoice,
        model: validModel,
        targetName: null,
        targetPhoneNumber: null,
        scriptType: 'outbound',
        scriptId: 'default'
    };
}

// 2. INBOUND CALL Configuration
function getInboundCallConfig(deps) {
    try {
        const currentScript = deps.scriptManager.getCurrentIncomingScript();
        if (currentScript) {
            return deps.scriptManager.getScriptConfig(currentScript.id, true);
        }
    } catch (error) {
        console.warn('⚠️ Error getting inbound script config:', error);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice('Kore'); // Default voice for inbound
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // Fallback to default inbound config - no hardcoded instructions
    return {
        aiInstructions: '', // Campaign script should provide all instructions
        voice: validVoice,
        model: validModel,
        targetName: null,
        targetPhoneNumber: null,
        scriptType: 'inbound',
        scriptId: 'customer-service'
    };
}

// 3. OUTBOUND TESTING Configuration
function getOutboundTestConfig(deps) {
    try {
        console.log('🔍 DEBUG: Getting current outbound script...');
        const currentScript = deps.scriptManager.getCurrentOutboundScript();
        console.log('🔍 DEBUG: Current outbound script:', !!currentScript, currentScript?.id);
        if (currentScript) {
            console.log('🔍 DEBUG: Getting script config for outbound ID:', currentScript.id);
            const config = deps.scriptManager.getScriptConfig(currentScript.id, false);
            console.log('🔍 DEBUG: Outbound script config result:', !!config, config?.aiInstructions?.length);
            return {
                ...config,
                isTestMode: true
            };
        }
    } catch (error) {
        console.warn('⚠️ Error getting outbound test config:', error);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(deps.GEMINI_DEFAULT_VOICE);
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // DEBUG: Check what the model manager is actually returning
    console.log(`🔍 DEBUG: deps.GEMINI_DEFAULT_MODEL = "${deps.GEMINI_DEFAULT_MODEL}"`);
    console.log(`🔍 DEBUG: validModel from manager = "${validModel}"`);
    console.log(`🔍 DEBUG: modelManager.getDefaultModel() = "${deps.modelManager.getDefaultModel()}"`);
    console.log(`🔍 DEBUG: process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);

    // Fallback to default outbound test config with basic test instructions
    return {
        aiInstructions: 'You are an AI assistant for testing outbound calls. Please respond naturally to any audio input you receive.',
        voice: validVoice,
        model: validModel,
        targetName: 'Test Contact',
        targetPhoneNumber: '+1234567890',
        scriptType: 'outbound',
        scriptId: 'default',
        isTestMode: true
    };
}

// 4. INBOUND TESTING Configuration
function getInboundTestConfig(deps) {
    try {
        console.log(`🔍 DEBUG: Getting current incoming script...`);
        const currentScript = deps.scriptManager.getCurrentIncomingScript();
        console.log(`🔍 DEBUG: Current script:`, !!currentScript, currentScript?.id);
        if (currentScript) {
            console.log(`🔍 DEBUG: Getting script config for ID: ${currentScript.id}`);
            const config = deps.scriptManager.getScriptConfig(currentScript.id, true);
            console.log(`🔍 DEBUG: Script config result:`, !!config);
            return {
                ...config,
                isTestMode: true
            };
        }
    } catch (error) {
        console.warn('⚠️ Error getting inbound test config:', error);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice('Kore'); // Default voice for inbound
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // Fallback to default inbound test config - no hardcoded instructions
    return {
        aiInstructions: '', // Campaign script should provide all instructions
        voice: validVoice,
        model: validModel,
        targetName: 'Test Caller',
        targetPhoneNumber: '+1234567890',
        scriptType: 'inbound',
        scriptId: 'customer-service',
        isTestMode: true
    };
}

// End session and cleanup using lifecycle manager
async function endSession(sessionId, deps, reason = 'user_requested') {
    const { lifecycleManager, activeConnections } = deps;

    try {
        console.log(`🔚 [${sessionId}] Ending session via lifecycle manager (reason: ${reason})`);

        const connectionData = activeConnections.get(sessionId);
        if (connectionData) {
            // Use lifecycle manager for proper session ending with summary generation
            const success = await lifecycleManager.requestSessionEnd(sessionId, connectionData, reason);

            if (success) {
                // Remove from active connections after lifecycle manager handles cleanup
                activeConnections.delete(sessionId);
                console.log(`✅ [${sessionId}] Session ended successfully via lifecycle manager`);
            } else {
                console.warn(`⚠️ [${sessionId}] Session end request failed, forcing cleanup`);
                await lifecycleManager.forceEndSession(sessionId, connectionData, 'force_cleanup');
                activeConnections.delete(sessionId);
            }
        } else {
            console.warn(`⚠️ [${sessionId}] No connection data found for session end`);
        }

    } catch (error) {
        console.error(`❌ [${sessionId}] Error ending session:`, error);
        // Force cleanup on error
        try {
            const connectionData = activeConnections.get(sessionId);
            if (lifecycleManager) {
                await lifecycleManager.forceEndSession(sessionId, connectionData, 'error_cleanup');
            }
            activeConnections.delete(sessionId);
        } catch (cleanupError) {
            console.error(`❌ [${sessionId}] Error during force cleanup:`, cleanupError);
        }
    }
}
